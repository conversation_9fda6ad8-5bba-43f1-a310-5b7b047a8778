import React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import {
  type ProgressBarActiveColor,
  ProgressBarBadge,
  ProgressBarBadgeTheme,
  ProgressBarHeader,
  type ProgressBarProps,
  ProgressBarRoot,
  type ProgressBarSize,
  ProgressBarSubtitle,
  ProgressBarTitle,
} from './ProgressBar';

const progressBarColors: ProgressBarActiveColor[] = ['primary', 'success', 'warning', 'danger', 'secondary'];
const progressBarSizes: ProgressBarSize[] = ['small', 'medium', 'large'];

const meta: Meta<typeof ProgressBarRoot> = {
  title: 'Status/ProgressBar',
  component: ProgressBarRoot,
  parameters: {
    docs: {
      description: {
        component: `
The ProgressBar component displays the completion progress of a task. It supports two variants:

- **Linear**: Traditional horizontal progress bar
- **Donut**: Circular progress indicator with optional percentage display

## Features

- Multiple color themes (primary, success, warning, danger, secondary)
- Three sizes (small, medium, large)
- Disabled state support
- Optional title, subtitle, and badges
- Progress percentage display
- Accessibility support with ARIA attributes

## Usage

\`\`\`tsx
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';

// Linear progress bar
<ProgressBar.Root progress={75} color="primary" size="medium">
  <ProgressBar.Header showProgress>
    <ProgressBar.Title>Task Progress</ProgressBar.Title>
    <ProgressBar.Subtitle>3 of 4 items completed</ProgressBar.Subtitle>
  </ProgressBar.Header>
</ProgressBar.Root>

// Donut progress bar
<ProgressBar.Root progress={75} variant="donut" color="success" size="large">
  <ProgressBar.Header>
    <ProgressBar.Title>Project Completion</ProgressBar.Title>
  </ProgressBar.Header>
</ProgressBar.Root>
\`\`\`
        `,
      },
    },
  },
  argTypes: {
    progress: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'Progress value from 0 to 100',
    },
    variant: {
      control: { type: 'select' },
      options: ['linear', 'donut'],
      description: 'Visual style of the progress bar',
    },
    color: {
      control: { type: 'select' },
      options: progressBarColors,
      description: 'Color theme of the progress bar',
    },
    size: {
      control: { type: 'select' },
      options: progressBarSizes,
      description: 'Size of the progress bar',
    },
    disabled: {
      control: { type: 'boolean' },
      description: 'Whether the progress bar is disabled',
    },
  },
};
export default meta;
type Story = StoryObj<ProgressBarProps>;

export const Default: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => <ProgressBarRoot {...args} />,
};

export const Colors: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-1">
          <span className="text-white">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={index * 10 + 20} />

          <div className="flex flex-col gap-1">
            <span className="text-white">{color} - disabled</span>
            <ProgressBarRoot {...args} color={color} progress={index * 10 + 20} disabled />
          </div>
        </div>
      ))}
    </div>
  ),
};

export const Sizes: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <span className="text-white">{size}</span>
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20} />
        </div>
      ))}
    </div>
  ),
};

export const WithPercentage: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20}>
            <ProgressBarHeader showProgress>
              <ProgressBarTitle>{size}</ProgressBarTitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      ))}
    </div>
  ),
};

export const WithTitleAndSubtitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
        <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithTitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithBadge: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>
          Subtitle
          <ProgressBarBadge label="title" theme={ProgressBarBadgeTheme.BLUE} />
        </ProgressBarTitle>
        <ProgressBarSubtitle>
          Subtitle
          <ProgressBarBadge label="subtitle" theme={ProgressBarBadgeTheme.GREEN} />
        </ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};
export const DarkMode: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div data-mode="dark" className="bg-gray-800 p-10">
      <ProgressBarRoot {...args}>
        <ProgressBarHeader showProgress>
          <ProgressBarTitle>Title</ProgressBarTitle>
          <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
        </ProgressBarHeader>
      </ProgressBarRoot>
    </div>
  ),
};
export const Disabled: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args} disabled>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
        <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

// Donut Variant Stories
export const DonutDefault: Story = {
  args: {
    progress: 65,
    variant: 'donut',
    size: 'medium',
    color: 'primary',
  },
  parameters: {
    docs: {
      description: {
        story: 'Basic donut progress bar with default styling.',
      },
    },
  },
  render: (args) => <ProgressBarRoot {...args} />,
};

export const DonutSizes: Story = {
  args: {
    progress: 75,
    variant: 'donut',
    color: 'primary',
  },
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bars in different sizes. Note that small size does not show percentage text.',
      },
    },
  },
  render: (args) => (
    <div className="flex flex-row gap-8 items-center">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-2 items-center">
          <span className="text-sm font-medium text-neutral">{size}</span>
          <ProgressBarRoot {...args} size={size} progress={60 + index * 10} />
        </div>
      ))}
    </div>
  ),
};

export const DonutColors: Story = {
  args: {
    progress: 80,
    variant: 'donut',
    size: 'large',
  },
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bars in different color themes.',
      },
    },
  },
  render: (args) => (
    <div className="flex flex-row gap-8 items-center flex-wrap">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-2 items-center">
          <span className="text-sm font-medium text-neutral capitalize">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={50 + index * 10} />
        </div>
      ))}
    </div>
  ),
};

export const DonutWithTitles: Story = {
  args: {
    progress: 85,
    variant: 'donut',
    size: 'large',
    color: 'success',
  },
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bar with title and subtitle. The content is centered below the donut.',
      },
    },
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader>
        <ProgressBarTitle>Project Completion</ProgressBarTitle>
        <ProgressBarSubtitle>85% Complete</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const DonutWithBadges: Story = {
  args: {
    progress: 92,
    variant: 'donut',
    size: 'large',
    color: 'primary',
  },
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bar with badges in the title and subtitle.',
      },
    },
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader>
        <ProgressBarTitle>
          Task Progress
          <ProgressBarBadge label="Active" theme={ProgressBarBadgeTheme.GREEN} />
        </ProgressBarTitle>
        <ProgressBarSubtitle>
          Phase 3
          <ProgressBarBadge label="Final" theme={ProgressBarBadgeTheme.BLUE} />
        </ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const DonutDisabled: Story = {
  args: {
    progress: 45,
    variant: 'donut',
    size: 'large',
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bars in disabled state across different colors. Disabled state uses muted colors.',
      },
    },
  },
  render: (args) => (
    <div className="flex flex-row gap-8 items-center flex-wrap">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-2 items-center">
          <span className="text-sm font-medium text-neutral capitalize">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={40 + index * 10}>
            <ProgressBarHeader>
              <ProgressBarTitle>Disabled</ProgressBarTitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      ))}
    </div>
  ),
};

export const VariantComparison: Story = {
  args: {
    progress: 70,
    color: 'primary',
    size: 'medium',
  },
  parameters: {
    docs: {
      description: {
        story: 'Side-by-side comparison of linear and donut variants with the same progress value.',
      },
    },
  },
  render: (args) => (
    <div className="flex flex-col gap-8">
      <div className="flex flex-row gap-8 items-center">
        <div className="flex flex-col gap-2 min-w-[200px]">
          <span className="text-sm font-medium text-neutral">Linear Variant</span>
          <ProgressBarRoot {...args} variant="linear">
            <ProgressBarHeader showProgress>
              <ProgressBarTitle>Linear Progress</ProgressBarTitle>
              <ProgressBarSubtitle>Traditional horizontal bar</ProgressBarSubtitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>

        <div className="flex flex-col gap-2 items-center">
          <span className="text-sm font-medium text-neutral">Donut Variant</span>
          <ProgressBarRoot {...args} variant="donut">
            <ProgressBarHeader>
              <ProgressBarTitle>Donut Progress</ProgressBarTitle>
              <ProgressBarSubtitle>Circular indicator</ProgressBarSubtitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      </div>
    </div>
  ),
};

export const DonutProgressStates: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Donut progress bars showing different completion states from 0% to 100%.',
      },
    },
  },
  render: () => (
    <div className="flex flex-row gap-6 items-center flex-wrap">
      {[0, 25, 50, 75, 100].map((progress) => (
        <div key={progress} className="flex flex-col gap-2 items-center">
          <span className="text-sm font-medium text-neutral">{progress}%</span>
          <ProgressBarRoot
            progress={progress}
            variant="donut"
            size="medium"
            color={progress === 100 ? 'success' : progress >= 75 ? 'primary' : progress >= 50 ? 'warning' : 'secondary'}
          >
            <ProgressBarHeader>
              <ProgressBarTitle>
                {progress === 0 ? 'Not Started' :
                  progress === 100 ? 'Complete' :
                    progress >= 75 ? 'Almost Done' :
                      progress >= 50 ? 'In Progress' : 'Getting Started'}
              </ProgressBarTitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      ))}
    </div>
  ),
};
